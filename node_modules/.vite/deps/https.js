import {
  __require
} from "./chunk-DGUM43GV.js";

// node_modules/.vite-electron-renderer/https.mjs
var avoid_parse_require = __require;
var _M_ = avoid_parse_require("https");
var Agent = _M_.Agent;
var globalAgent = _M_.globalAgent;
var Server = _M_.Server;
var createServer = _M_.createServer;
var keyword_get = _M_.get;
var request = _M_.request;
var keyword_default = _M_.default || _M_;
export {
  Agent,
  Server,
  createServer,
  keyword_default as default,
  keyword_get as get,
  globalAgent,
  request
};
//# sourceMappingURL=https.js.map
