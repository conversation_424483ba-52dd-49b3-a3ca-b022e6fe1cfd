{"version": 3, "sources": ["../../.vite-electron-renderer/https.mjs"], "sourcesContent": ["const avoid_parse_require = require; const _M_ = avoid_parse_require(\"https\");\nexport const Agent = _M_.Agent;\nexport const globalAgent = _M_.globalAgent;\nexport const Server = _M_.Server;\nexport const createServer = _M_.createServer;\nconst keyword_get = _M_.get;\nexport const request = _M_.request;\nconst keyword_default = _M_.default || _M_;\nexport {\n  keyword_get as get,\n  keyword_default as default,\n};"], "mappings": ";;;;;AAAA,IAAM,sBAAsB;AAAS,IAAM,MAAM,oBAAoB,OAAO;AACrE,IAAM,QAAQ,IAAI;AAClB,IAAM,cAAc,IAAI;AACxB,IAAM,SAAS,IAAI;AACnB,IAAM,eAAe,IAAI;AAChC,IAAM,cAAc,IAAI;AACjB,IAAM,UAAU,IAAI;AAC3B,IAAM,kBAAkB,IAAI,WAAW;", "names": []}