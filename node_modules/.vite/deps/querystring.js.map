{"version": 3, "sources": ["../../.vite-electron-renderer/querystring.mjs"], "sourcesContent": ["const avoid_parse_require = require; const _M_ = avoid_parse_require(\"querystring\");\nexport const unescapeBuffer = _M_.unescapeBuffer;\nexport const unescape = _M_.unescape;\nexport const escape = _M_.escape;\nexport const stringify = _M_.stringify;\nexport const encode = _M_.encode;\nexport const parse = _M_.parse;\nexport const decode = _M_.decode;\nconst keyword_default = _M_.default || _M_;\nexport {\n  keyword_default as default,\n};"], "mappings": ";;;;;AAAA,IAAM,sBAAsB;AAAS,IAAM,MAAM,oBAAoB,aAAa;AAC3E,IAAM,iBAAiB,IAAI;AAC3B,IAAM,WAAW,IAAI;AACrB,IAAM,SAAS,IAAI;AACnB,IAAM,YAAY,IAAI;AACtB,IAAM,SAAS,IAAI;AACnB,IAAM,QAAQ,IAAI;AAClB,IAAM,SAAS,IAAI;AAC1B,IAAM,kBAAkB,IAAI,WAAW;", "names": []}