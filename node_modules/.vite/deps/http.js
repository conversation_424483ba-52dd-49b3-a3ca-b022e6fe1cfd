import {
  __require
} from "./chunk-DGUM43GV.js";

// node_modules/.vite-electron-renderer/http.mjs
var avoid_parse_require = __require;
var _M_ = avoid_parse_require("http");
var _connectionListener = _M_._connectionListener;
var METHODS = _M_.METHODS;
var STATUS_CODES = _M_.STATUS_CODES;
var Agent = _M_.Agent;
var ClientRequest = _M_.ClientRequest;
var IncomingMessage = _M_.IncomingMessage;
var OutgoingMessage = _M_.OutgoingMessage;
var Server = _M_.Server;
var ServerResponse = _M_.ServerResponse;
var createServer = _M_.createServer;
var validateHeaderName = _M_.validateHeaderName;
var validateHeaderValue = _M_.validateHeaderValue;
var keyword_get = _M_.get;
var request = _M_.request;
var maxHeaderSize = _M_.maxHeaderSize;
var globalAgent = _M_.globalAgent;
var keyword_default = _M_.default || _M_;
export {
  Agent,
  ClientRequest,
  IncomingMessage,
  METHODS,
  OutgoingMessage,
  STATUS_CODES,
  Server,
  ServerResponse,
  _connectionListener,
  createServer,
  keyword_default as default,
  keyword_get as get,
  globalAgent,
  maxHeaderSize,
  request,
  validateHeaderName,
  validateHeaderValue
};
//# sourceMappingURL=http.js.map
