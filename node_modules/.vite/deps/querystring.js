import {
  __require
} from "./chunk-DGUM43GV.js";

// node_modules/.vite-electron-renderer/querystring.mjs
var avoid_parse_require = __require;
var _M_ = avoid_parse_require("querystring");
var unescapeBuffer = _M_.unescapeBuffer;
var unescape = _M_.unescape;
var escape = _M_.escape;
var stringify = _M_.stringify;
var encode = _M_.encode;
var parse = _M_.parse;
var decode = _M_.decode;
var keyword_default = _M_.default || _M_;
export {
  decode,
  keyword_default as default,
  encode,
  escape,
  parse,
  stringify,
  unescape,
  unescapeBuffer
};
//# sourceMappingURL=querystring.js.map
