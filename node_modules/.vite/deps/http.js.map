{"version": 3, "sources": ["../../.vite-electron-renderer/http.mjs"], "sourcesContent": ["const avoid_parse_require = require; const _M_ = avoid_parse_require(\"http\");\nexport const _connectionListener = _M_._connectionListener;\nexport const METHODS = _M_.METHODS;\nexport const STATUS_CODES = _M_.STATUS_CODES;\nexport const Agent = _M_.Agent;\nexport const ClientRequest = _M_.ClientRequest;\nexport const IncomingMessage = _M_.IncomingMessage;\nexport const OutgoingMessage = _M_.OutgoingMessage;\nexport const Server = _M_.Server;\nexport const ServerResponse = _M_.ServerResponse;\nexport const createServer = _M_.createServer;\nexport const validateHeaderName = _M_.validateHeaderName;\nexport const validateHeaderValue = _M_.validateHeaderValue;\nconst keyword_get = _M_.get;\nexport const request = _M_.request;\nexport const maxHeaderSize = _M_.maxHeaderSize;\nexport const globalAgent = _M_.globalAgent;\nconst keyword_default = _M_.default || _M_;\nexport {\n  keyword_get as get,\n  keyword_default as default,\n};"], "mappings": ";;;;;AAAA,IAAM,sBAAsB;AAAS,IAAM,MAAM,oBAAoB,MAAM;AACpE,IAAM,sBAAsB,IAAI;AAChC,IAAM,UAAU,IAAI;AACpB,IAAM,eAAe,IAAI;AACzB,IAAM,QAAQ,IAAI;AAClB,IAAM,gBAAgB,IAAI;AAC1B,IAAM,kBAAkB,IAAI;AAC5B,IAAM,kBAAkB,IAAI;AAC5B,IAAM,SAAS,IAAI;AACnB,IAAM,iBAAiB,IAAI;AAC3B,IAAM,eAAe,IAAI;AACzB,IAAM,qBAAqB,IAAI;AAC/B,IAAM,sBAAsB,IAAI;AACvC,IAAM,cAAc,IAAI;AACjB,IAAM,UAAU,IAAI;AACpB,IAAM,gBAAgB,IAAI;AAC1B,IAAM,cAAc,IAAI;AAC/B,IAAM,kBAAkB,IAAI,WAAW;", "names": []}