// 测试YAML转换功能
const fs = require('fs');
const yaml = require('js-yaml');

// 模拟 removeFieldsRecursively 函数
function removeFieldsRecursively(node, fieldsToRemove) {
  if (!node || typeof node !== 'object') {
    return node;
  }

  // 创建节点的浅拷贝
  const cleanedNode = { ...node };

  // 删除通用字段
  fieldsToRemove.forEach(field => {
    delete cleanedNode[field];
  });

  // 如果节点类型是VECTOR，删除额外的字段
  if (cleanedNode.type === 'VECTOR') {
    const vectorFieldsToRemove = [
      'blendMode',
      'fills',
      'strokes',
      'strokeWeight',
      'strokeAlign'
    ];

    vectorFieldsToRemove.forEach(field => {
      delete cleanedNode[field];
    });
  }

  // 如果有children，递归处理每个子节点
  if (cleanedNode.children && Array.isArray(cleanedNode.children)) {
    cleanedNode.children = cleanedNode.children.map(child =>
      removeFieldsRecursively(child, fieldsToRemove)
    );
  }

  return cleanedNode;
}

// 模拟 processFigmaData 函数
function processFigmaData(figmaResponse) {
  const result = [];
  
  // 要删除的字段列表
  const fieldsToRemove = ['constraints', 'absoluteRenderBounds', 'effects', 'interactions', 'scrollBehavior'];

  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return result;
  }

  // 遍历所有节点
  Object.keys(figmaResponse.nodes).forEach(nodeId => {
    const nodeInfo = figmaResponse.nodes[nodeId];

    // 检查是否有document和children
    if (nodeInfo && nodeInfo.document && nodeInfo.document.children && Array.isArray(nodeInfo.document.children)) {
      const document = nodeInfo.document;
      const children = document.children;

      // 为每个子元素创建一个新的document结构，只包含document部分
      children.forEach(child => {
        // 先清理子节点中的指定字段
        const cleanedChild = removeFieldsRecursively(child, fieldsToRemove);

        // 清理document本身的指定字段
        const cleanedDocument = removeFieldsRecursively(document, fieldsToRemove);

        const newDocumentStructure = {
          document: {
            ...cleanedDocument, // 保持原有的document属性（已清理）
            children: [cleanedChild] // 只包含当前子元素（已清理）
          }
        };

        // 转换为YAML格式并添加到结果数组
        const yamlString = yaml.dump(newDocumentStructure, {
          indent: 2,
          lineWidth: -1, // 不限制行宽
          noRefs: true,  // 不使用引用
          skipInvalid: true // 跳过无效值
        });
        result.push(yamlString);
      });
    } else {
      // 如果没有children或children不是数组，只返回document结构
      if (nodeInfo && nodeInfo.document) {
        // 清理document中的指定字段
        const cleanedDocument = removeFieldsRecursively(nodeInfo.document, fieldsToRemove);

        const documentStructure = {
          document: cleanedDocument
        };

        // 转换为YAML格式并添加到结果数组
        const yamlString = yaml.dump(documentStructure, {
          indent: 2,
          lineWidth: -1, // 不限制行宽
          noRefs: true,  // 不使用引用
          skipInvalid: true // 跳过无效值
        });
        result.push(yamlString);
      }
    }
  });

  return result;
}

// 读取测试数据
const testData = JSON.parse(fs.readFileSync('2.json', 'utf8'));

// 模拟Figma API响应格式
const mockResponse = {
  nodes: {
    '46:6': testData
  }
};

// 处理数据
const yamlResults = processFigmaData(mockResponse);

console.log('处理结果数量:', yamlResults.length);
console.log('\n第一个YAML结果:');
console.log(yamlResults[0]);

// 计算压缩效果
const originalSize = JSON.stringify(testData).length;
const yamlSize = yamlResults.join('').length;
console.log(`\n压缩效果:`);
console.log(`原始JSON大小: ${originalSize} 字符`);
console.log(`YAML总大小: ${yamlSize} 字符`);
console.log(`压缩率: ${((originalSize - yamlSize) / originalSize * 100).toFixed(2)}%`);
