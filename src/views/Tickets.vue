<template>
  <div class="page">
    <!-- 输入界面 -->
    <div v-if="!showComparison" class="input-container">
      <div class="input-section">
        <h1 class="title">UI 设计对比工具</h1>
        <p class="description">输入页面链接开始对比设计稿与AI实现效果</p>

        <div class="input-group">
          <input
            v-model="urlInput"
            type="url"
            placeholder="请输入Figma分享链接"
            class="url-input"
            @keyup.enter="startComparison"
          >
        </div>
      </div>
    </div>

    <!-- 对比界面 -->
    <div v-else class="container">
      <div class="back-button" @click="goBack">
        <span>← 返回</span>
      </div>

      <div class="comparison-container">
        <div class="left-section">
          <div class="phone-mockup">
            <div class="phone-screen" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">
              <div id="design-placeholder" class="phone-placeholder" v-show="!designImageSrc">
                <div style="font-size: 32px; margin-bottom: 10px;">📱</div>
                <p>拖拽或上传设计稿</p>
              </div>
              <img
                id="design-image"
                class="design-image"
                v-show="designImageSrc"
                :src="designImageSrc"
                alt="设计稿"
              >
            </div>
            <div class="phone-buttons">
              <div class="phone-button volume-up"></div>
              <div class="phone-button volume-down"></div>
              <div class="phone-button power"></div>
            </div>
          </div>
        </div>

        <div class="right-section">
          <div class="phone-mockup">
            <div class="phone-screen">
              <div id="iframe-placeholder" class="phone-placeholder-iframe" v-show="!showIframe">
                <div style="font-size: 32px; margin-bottom: 10px;">🌐</div>
                <p>加载页面中...</p>
              </div>
              <iframe
                ref="implementationIframe"
                id="implementation-iframe"
                class="implementation-iframe"
                v-show="showIframe"
                :src="iframeSrc"
              ></iframe>
            </div>
            <div class="phone-buttons">
              <div class="phone-button volume-up"></div>
              <div class="phone-button volume-down"></div>
              <div class="phone-button power"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { printFigmaInfo, parseFigmaUrl } from '../api/figma'

// 界面状态
const showComparison = ref(false)
const urlInput = ref('')

// 对比页面状态
const designImageSrc = ref('')
const showIframe = ref(false)
const iframeSrc = ref('')
const implementationIframe = ref<HTMLIFrameElement | null>(null)

// 开始对比
const startComparison = async () => {
  if (!urlInput.value.trim()) return

  const inputUrl = urlInput.value.trim()

  try {
    // 检查是否是Figma链接
    if (inputUrl.includes('figma.com')) {
      console.log('🔍 检测到Figma链接，正在获取设计稿信息...')

      // 调用Figma API获取文件信息并打印
      await printFigmaInfo(inputUrl)

      // 解析Figma链接获取基本信息
      const linkInfo = parseFigmaUrl(inputUrl)
      console.log('📋 解析结果:', linkInfo)

      // 这里可以根据需要设置设计稿图片
      // 如果有缩略图URL，可以设置为设计稿
      // designImageSrc.value = thumbnailUrl

    } else {
      console.log('🌐 检测到普通网页链接')
    }

    // 设置iframe源
    iframeSrc.value = inputUrl
    showComparison.value = true
    showIframe.value = true

    // 等待DOM更新后设置iframe
    nextTick(() => {
      if (implementationIframe.value) {
        setupMobileIframe(implementationIframe.value)
      }
    })

  } catch (error) {
    console.error('❌ 处理链接时出错:', error)
    // 即使出错也继续显示对比界面
    iframeSrc.value = inputUrl
    showComparison.value = true
    showIframe.value = true

    nextTick(() => {
      if (implementationIframe.value) {
        setupMobileIframe(implementationIframe.value)
      }
    })
  }
}

// 返回输入界面
const goBack = () => {
  showComparison.value = false
  showIframe.value = false
  designImageSrc.value = ''
}

// 设置iframe为手机模式
const setupMobileIframe = (iframe: HTMLIFrameElement) => {
  iframe.onload = function() {
    try {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) return

      // 检查是否已经有viewport meta标签
      let viewportMeta = iframeDoc.querySelector('meta[name="viewport"]') as HTMLMetaElement
      if (!viewportMeta) {
        viewportMeta = iframeDoc.createElement('meta')
        viewportMeta.name = 'viewport'
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        iframeDoc.head.appendChild(viewportMeta)
      } else {
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      }

      // 添加移动端样式
      const mobileStyle = iframeDoc.createElement('style')
      mobileStyle.textContent = `
        body {
          -webkit-text-size-adjust: 100%;
          -ms-text-size-adjust: 100%;
          touch-action: manipulation;
        }
        * {
          -webkit-tap-highlight-color: transparent;
        }
      `
      iframeDoc.head.appendChild(mobileStyle)
    } catch (e) {
      // 跨域限制，无法修改iframe内容
      console.log('无法修改iframe内容，可能是跨域限制')
    }
  }
}

// 拖拽处理函数
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  const target = e.currentTarget as HTMLElement
  target.style.backgroundColor = '#f0f9ff'
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  const target = e.currentTarget as HTMLElement
  target.style.backgroundColor = '#fafafa'
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  const target = e.currentTarget as HTMLElement
  target.style.backgroundColor = '#fafafa'

  const files = e.dataTransfer?.files
  if (files && files.length > 0 && files[0].type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = function(e) {
      designImageSrc.value = e.target?.result as string
    }
    reader.readAsDataURL(files[0])
  }
}

// 页面加载完成后的初始化
onMounted(() => {
  // 初始状态不需要特殊处理
})
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: white;
}

/* 输入界面样式 */
.input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #2d2d2d;
}

.input-section {
  text-align: center;
  padding: 40px;
  margin-top: -60px 
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 16px;
  color: white;
}

.description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 28px;
  line-height: 1.5;
}

.input-group {
  display: flex;
  gap: 12px;
  width: 800px;
  margin-bottom: 32px;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 16px 20px;
  font-size: 16px;
  border: 2px solid #404040;
  border-radius: 8px;
  background: #1a1a1a;
  color: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.url-input:focus {
  border-color: #007bff;
}

.url-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.start-btn {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 500;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.start-btn:hover:not(:disabled) {
  background: #0056b3;
}

.start-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tips p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background: rgba(0, 0, 0, 0.9);
}

.container {
  width: 100%;
  height: 100vh;
  margin: 0;
  background: #fafafa;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

.comparison-container {
  display: flex;
  height: 100vh;
}

.left-section {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding-top: 55px;
  padding-right: 20px;
  background: #fafafa;
}

.right-section {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-top: 55px;
  padding-left: 20px;
  background: #fafafa;
}

.phone-mockup {
  position: relative;
  width: 470px;
  height: 972px;
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border-radius: 50px;
  padding: 20px;
  box-shadow:
    0 0 0 2px #333,
    0 0 0 4px #1a1a1a,
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
}

.phone-mockup::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 30px;
  background: #1a1a1a;
  border-radius: 15px;
  z-index: 2;
}

.phone-mockup::after {
  content: '';
  position: absolute;
  top: 22px;
  left: 50%;
  transform: translateX(-50%);
  width: 15px;
  height: 15px;
  background: #333;
  border-radius: 50%;
  z-index: 3;
}

.phone-screen {
  width: 430px;
  height: 932px;
  background: #000;
  border-radius: 35px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.design-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0;
}

.phone-buttons {
  position: absolute;
  right: -3px;
  top: 120px;
}

.phone-button {
  width: 3px;
  background: #333;
  margin-bottom: 15px;
  border-radius: 2px;
}

.phone-button.volume-up {
  height: 30px;
}

.phone-button.volume-down {
  height: 30px;
}

.phone-button.power {
  position: absolute;
  right: 0;
  top: 80px;
  height: 50px;
}

.implementation-iframe {
  width: 430px;
  height: 932px;
  border: none;
  border-radius: 0;
  background: white;
  transform: scale(1);
  transform-origin: top left;
}

.phone-placeholder-iframe {
  color: #666;
  font-size: 14px;
  text-align: center;
}

.placeholder {
  text-align: center;
  color: #6b7280;
  font-size: 16px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 10px;
  opacity: 0.5;
}

.phone-placeholder {
  color: #666;
  font-size: 14px;
  text-align: center;
}
</style>
