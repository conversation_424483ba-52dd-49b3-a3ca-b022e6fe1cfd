import { makeHttpRequest } from './http-client'
import type { FigmaNodesResponse, FigmaNodeInfo, FigmaNode } from '../../type/figma'

// Figma API配置
const FIGMA_TOKEN = "*********************************************"

export interface FigmaApiRequestParams {
  url: string
  params?: Record<string, any>
  headers?: Record<string, string>
}

export interface FigmaUrlParseResult {
  fileKey: string
  nodeId?: string
  originalUrl: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

/**
 * 发送 Figma API 请求
 */
export async function figmaApiRequest({ url, params, headers }: FigmaApiRequestParams): Promise<ApiResponse> {
  try {
    const response = await makeHttpRequest(url, {
      params,
      headers: {
        'X-FIGMA-TOKEN': FIGMA_TOKEN,
        ...headers
      }
    })
    return { success: true, data: response.data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 解析 Figma 链接，提取文件密钥和节点ID
 */
export async function parseFigmaUrl(url: string): Promise<ApiResponse<FigmaUrlParseResult>> {
  try {
    const parsedUrl = new URL(url)

    if (!parsedUrl.hostname.includes('figma.com')) {
      throw new Error('不是有效的 Figma 链接')
    }

    const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0)

    if (pathParts.length < 2 || !['file', 'design'].includes(pathParts[0])) {
      throw new Error('无效的 Figma 链接格式')
    }

    const fileKey = pathParts[1]

    let nodeId: string | undefined
    const nodeIdParam = parsedUrl.searchParams.get('node-id')
    if (nodeIdParam) {
      nodeId = decodeURIComponent(nodeIdParam).replace(':', '-')
    }

    return {
      success: true,
      data: {
        fileKey,
        nodeId,
        originalUrl: url
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 递归删除节点中的指定字段
 * @param node 节点对象
 * @param fieldsToRemove 要删除的字段数组
 * @returns 清理后的节点对象
 */
function removeFieldsRecursively(node: any, fieldsToRemove: string[]): any {
  if (!node || typeof node !== 'object') {
    return node
  }

  // 创建节点的浅拷贝
  const cleanedNode = { ...node }

  // 删除通用字段
  fieldsToRemove.forEach(field => {
    delete cleanedNode[field]
  })

  // 如果节点类型是VECTOR，删除额外的字段
  if (cleanedNode.type === 'VECTOR') {
    const vectorFieldsToRemove = [
      'scrollBehavior',
      'blendMode',
      'fills',
      'strokes',
      'strokeWeight',
      'strokeAlign'
    ]

    vectorFieldsToRemove.forEach(field => {
      delete cleanedNode[field]
    })
  }

  // 如果有children，递归处理每个子节点
  if (cleanedNode.children && Array.isArray(cleanedNode.children)) {
    cleanedNode.children = cleanedNode.children.map((child: any) =>
      removeFieldsRecursively(child, fieldsToRemove)
    )
  }

  return cleanedNode
}

/**
 * 处理 Figma API 响应数据，将包含多个子元素的document节点拆分成多个只包含单个子元素的document节点
 * 同时递归删除指定的字段
 * @param figmaResponse Figma API 响应数据
 * @returns 处理后的数据数组，每个元素只包含document结构和一个子元素
 */
export function processFigmaData(figmaResponse: any): any[] {
  const result: any[] = []

  // 要删除的字段列表
  const fieldsToRemove = ['constraints', 'absoluteRenderBounds', 'effects', 'interactions']

  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return result
  }

  // 遍历所有节点
  Object.keys(figmaResponse.nodes).forEach(nodeId => {
    const nodeInfo = figmaResponse.nodes[nodeId]

    // 检查是否有document和children
    if (nodeInfo && nodeInfo.document && nodeInfo.document.children && Array.isArray(nodeInfo.document.children)) {
      const document = nodeInfo.document
      const children = document.children

      // 为每个子元素创建一个新的document结构，只包含document部分
      children.forEach((child: any) => {
        // 先清理子节点中的指定字段
        const cleanedChild = removeFieldsRecursively(child, fieldsToRemove)

        // 清理document本身的指定字段
        const cleanedDocument = removeFieldsRecursively(document, fieldsToRemove)

        const newDocumentStructure = {
          document: {
            ...cleanedDocument, // 保持原有的document属性（已清理）
            children: [cleanedChild] // 只包含当前子元素（已清理）
          }
        }

        result.push(newDocumentStructure)
      })
    } else {
      // 如果没有children或children不是数组，只返回document结构
      if (nodeInfo && nodeInfo.document) {
        // 清理document中的指定字段
        const cleanedDocument = removeFieldsRecursively(nodeInfo.document, fieldsToRemove)

        result.push({
          document: cleanedDocument
        })
      }
    }
  })

  return result
}
